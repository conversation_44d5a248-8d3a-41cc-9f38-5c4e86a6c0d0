# 价值平均+择时量化投资策略

## 策略概述

本策略是基于创业板ETF(159915)技术指标进行择时，结合价值平均投资方法的量化投资策略。策略在沉睡期持有红利国企ETF(510720)，在激活期持有创业板ETF(159915)并执行价值平均策略。

## 核心特性

### 1. 双阶段策略
- **沉睡期**：持有国泰上证国有企业红利ETF(510720)，等待买入信号
- **激活期**：持有易方达创业板ETF(159915)，执行价值平均策略

### 2. 技术指标择时
- 使用35期EMA指数移动平均线
- 买入信号：收盘价跌破EMA*0.85的底部线
- 卖出信号：最高价突破EMA*1.90的顶部线
- 信号过滤：买入信号8个周期内不重复，卖出信号10个周期内不重复

### 3. 价值平均策略
- 基于5年历史最高点作为起始期
- 每期投入固定金额（默认10,000元）
- 目标价值 = 期数 × 每期投入金额
- 根据当前价值与目标价值的差异进行买卖调整

### 4. 资金调用优先级
1. 优先卖出510720获取资金
2. 使用账户可用现金
3. 使用融资额度

## 文件结构

```
├── value_averaging_strategy.py  # 主策略文件
├── test_strategy.py            # 完整测试文件
├── simple_test.py              # 简单测试文件
├── README.md                   # 说明文档
└── docs/                       # 文档目录
    ├── 开发需求说明.md
    ├── 策略流程图.md
    ├── 数据库设计.md
    └── iQuant资料/
```

## 策略参数配置

### 基金代码
- `SLEEPING_FUND_CODE = "510720"`  # 沉睡期基金
- `ACTIVE_FUND_CODE = "159915"`    # 激活期基金
- `SIGNAL_FUND_CODE = "159915"`    # 信号检测基金

### 价值平均参数
- `PERIOD_INVESTMENT_AMOUNT = 10000`  # 每期投入金额（元）
- `INVESTMENT_CYCLE = "月线"`          # 投资周期
- `SLEEPING_POSITION_RATIO = 1.0`     # 沉睡期基金仓位比例

### 技术指标参数
- `EMA_DETECTION_CYCLE = "季线"`       # EMA检测周期
- `EMA_PERIOD = 35`                   # EMA参数
- `BOTTOM_RATIO = 0.85`               # 底部相对比例
- `TOP_RATIO = 1.90`                  # 顶部相对比例

### 信号过滤参数
- `BUY_SIGNAL_FILTER_PERIODS = 8`     # 买入信号过滤周期
- `SELL_SIGNAL_FILTER_PERIODS = 10`   # 卖出信号过滤周期

## 数据库设计

策略使用SQLite数据库存储以下信息：

1. **strategy_status** - 策略状态表
2. **signal_history** - 信号历史表
3. **trade_orders** - 交易指令表
4. **position_records** - 持仓记录表
5. **skip_periods** - 跳过周期表
6. **trade_logs** - 交易日志表
7. **account_info** - 账户信息表

## 使用方法

### 1. 在iQuant平台中使用

1. 将`value_averaging_strategy.py`文件上传到iQuant平台
2. 在策略编辑器中加载该文件
3. 设置账户ID和其他必要参数
4. 启动策略

### 2. 本地测试

```bash
# 运行简单测试
python simple_test.py

# 运行完整测试
python test_strategy.py
```

## 核心函数说明

### 主要函数
- `init(ContextInfo)` - 策略初始化函数
- `handlebar(ContextInfo)` - 主策略逻辑函数

### 技术指标模块
- `update_technical_indicators()` - 更新技术指标
- `calculate_ema()` - 计算EMA指标
- `detect_signals()` - 检测买卖信号
- `check_signal_filter()` - 信号过滤

### 价值平均模块
- `calculate_value_averaging()` - 价值平均计算
- `calculate_current_period()` - 计算当前期数
- `get_historical_highest_price()` - 获取历史最高价

### 交易执行模块
- `execute_trading_logic()` - 执行交易逻辑
- `execute_trade_order()` - 执行交易指令
- `execute_phase_transition()` - 执行阶段切换

### 状态管理模块
- `load_strategy_status()` - 加载策略状态
- `update_strategy_status()` - 更新策略状态
- `validate_strategy_state()` - 验证策略状态

## 风险提示

1. **市场风险**：策略基于历史数据和技术指标，无法保证未来收益
2. **流动性风险**：ETF可能存在流动性不足的情况
3. **系统风险**：依赖iQuant平台的稳定性和API可用性
4. **参数风险**：策略参数需要根据市场情况适时调整

## 注意事项

1. 策略需要在iQuant平台环境中运行
2. 确保账户有足够的资金和权限
3. 定期检查策略运行状态和日志
4. 根据市场情况调整策略参数
5. 建议先在模拟环境中测试

## 版本信息

- 版本：1.0.0
- 创建时间：2025-01-29
- 作者：AI Assistant
- 适用平台：国信iQuant策略交易平台

## 联系方式

如有问题或建议，请通过以下方式联系：
- 查看文档目录中的详细说明
- 运行测试文件验证功能
- 检查日志文件排查问题