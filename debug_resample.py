# -*- coding: utf-8 -*-
"""
调试重采样函数
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def debug_resample_function():
    """调试重采样函数的问题"""
    print("=== 调试重采样函数 ===")
    
    # 模拟iQuant API返回的数据格式
    # 根据技术文档，get_market_data_ex返回的是字典格式 {code: data}
    # 其中data是pandas DataFrame
    
    # 创建模拟的日线数据 (模拟API返回的DataFrame格式)
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    n_days = len(dates)
    
    # 模拟价格数据
    base_price = 10.0
    daily_data = pd.DataFrame({
        'open': base_price + np.random.randn(n_days) * 0.1,
        'high': base_price + np.random.randn(n_days) * 0.1 + 0.2,
        'low': base_price + np.random.randn(n_days) * 0.1 - 0.2,
        'close': base_price + np.random.randn(n_days) * 0.1,
        'volume': np.random.randint(1000, 10000, n_days),
        'amount': np.random.randint(10000, 100000, n_days)
    }, index=dates)
    
    print(f"原始日线数据:")
    print(f"  数据类型: {type(daily_data)}")
    print(f"  数据形状: {daily_data.shape}")
    print(f"  索引类型: {type(daily_data.index)}")
    print(f"  日期范围: {daily_data.index[0]} 到 {daily_data.index[-1]}")
    print(f"  前5行数据:")
    print(daily_data.head())
    
    # 测试重采样到季线
    print(f"\n=== 测试季线重采样 ===")
    try:
        quarterly_data = daily_data.resample('Q').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }).dropna()
        
        print(f"季线重采样结果:")
        print(f"  数据类型: {type(quarterly_data)}")
        print(f"  数据形状: {quarterly_data.shape}")
        print(f"  索引类型: {type(quarterly_data.index)}")
        print(f"  日期范围: {quarterly_data.index[0]} 到 {quarterly_data.index[-1]}")
        print(f"  季线数据:")
        print(quarterly_data)
        
    except Exception as e:
        print(f"❌ 季线重采样失败: {str(e)}")
    
    # 测试可能的问题场景
    print(f"\n=== 测试问题场景 ===")
    
    # 场景1: 数据没有正确的时间索引
    print("场景1: 数据没有时间索引")
    data_no_index = daily_data.reset_index(drop=True)
    print(f"  无时间索引数据形状: {data_no_index.shape}")
    print(f"  索引类型: {type(data_no_index.index)}")
    
    # 尝试重采样
    try:
        # 这应该会失败，因为没有时间索引
        result = data_no_index.resample('Q').agg({'close': 'last'})
        print(f"  重采样成功: {result.shape}")
    except Exception as e:
        print(f"  重采样失败 (预期): {str(e)}")
    
    # 场景2: 测试我们的修复逻辑
    print("\n场景2: 测试修复逻辑")
    if not isinstance(data_no_index.index, pd.DatetimeIndex):
        print("  检测到非时间索引，创建时间索引...")
        end_date = pd.Timestamp.now()
        start_date = end_date - pd.Timedelta(days=len(data_no_index)-1)
        data_no_index.index = pd.date_range(start=start_date, end=end_date, periods=len(data_no_index))
        print(f"  新索引类型: {type(data_no_index.index)}")
        print(f"  新日期范围: {data_no_index.index[0]} 到 {data_no_index.index[-1]}")
        
        # 现在尝试重采样
        try:
            result = data_no_index.resample('Q').agg({'close': 'last'}).dropna()
            print(f"  修复后重采样成功: {result.shape}")
            print(f"  结果:")
            print(result)
        except Exception as e:
            print(f"  修复后重采样仍失败: {str(e)}")

def test_actual_resample_function():
    """测试实际的重采样函数"""
    print(f"\n=== 测试实际的重采样函数 ===")
    
    # 导入重采样函数 (简化版本)
    def resample_daily_to_period(daily_data, period_type='1q'):
        import pandas as pd
        
        try:
            if daily_data is None:
                return None
                
            if not isinstance(daily_data, pd.DataFrame):
                if isinstance(daily_data, dict):
                    daily_data = pd.DataFrame(daily_data)
                else:
                    return daily_data
            
            if len(daily_data) == 0:
                return daily_data
                
            # 关键问题可能在这里！
            if not isinstance(daily_data.index, pd.DatetimeIndex):
                print(f"  警告: 数据没有时间索引，当前索引类型: {type(daily_data.index)}")
                end_date = pd.Timestamp.now()
                start_date = end_date - pd.Timedelta(days=len(daily_data)-1)
                daily_data.index = pd.date_range(start=start_date, end=end_date, periods=len(daily_data))
                print(f"  已创建时间索引: {daily_data.index[0]} 到 {daily_data.index[-1]}")
            
            if period_type == '1q':
                rule = 'Q'
            elif period_type == '1mon':
                rule = 'M'
            else:
                return daily_data
            
            agg_rules = {}
            for col in daily_data.columns:
                if col in ['open']:
                    agg_rules[col] = 'first'
                elif col in ['high']:
                    agg_rules[col] = 'max'
                elif col in ['low']:
                    agg_rules[col] = 'min'
                elif col in ['close']:
                    agg_rules[col] = 'last'
                elif col in ['volume', 'amount']:
                    agg_rules[col] = 'sum'
                else:
                    agg_rules[col] = 'last'
            
            print(f"  执行重采样: rule={rule}, agg_rules={agg_rules}")
            resampled = daily_data.resample(rule).agg(agg_rules).dropna()
            print(f"  重采样结果形状: {resampled.shape}")
            
            return resampled
            
        except Exception as e:
            print(f"  重采样失败: {str(e)}")
            return daily_data
    
    # 创建测试数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    daily_data = pd.DataFrame({
        'open': [10.0] * len(dates),
        'high': [10.5] * len(dates),
        'low': [9.5] * len(dates),
        'close': [10.0] * len(dates),
    }, index=dates)
    
    print(f"测试数据: {daily_data.shape}")
    
    # 测试重采样
    result = resample_daily_to_period(daily_data, '1q')
    print(f"重采样结果: {result.shape if result is not None else 'None'}")
    if result is not None:
        print(result)

if __name__ == "__main__":
    debug_resample_function()
    test_actual_resample_function()
