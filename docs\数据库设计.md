# 策略数据库设计

## 数据库表结构

### 1. strategy_status - 策略状态表
```sql
CREATE TABLE strategy_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    current_phase TEXT NOT NULL,           -- 当前阶段：'sleeping' 或 'active'
    last_check_time TEXT NOT NULL,         -- 最后检测时间
    first_activation_time TEXT,            -- 首次激活时间
    start_period_date TEXT,                -- 价值平均起始期日期（最高点日期）
    start_period_price REAL,              -- 起始期价格
    current_period INTEGER DEFAULT 0,      -- 当前期数
    created_time TEXT NOT NULL,           -- 创建时间
    updated_time TEXT NOT NULL            -- 更新时间
);
```

### 2. signal_history - 信号历史表
```sql
CREATE TABLE signal_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    signal_date TEXT NOT NULL,            -- 信号日期
    signal_type TEXT NOT NULL,            -- 信号类型：'ENTERLONG' 或 'EXITLONG'
    signal_price REAL NOT NULL,           -- 信号价格
    ema_value REAL NOT NULL,              -- EMA值
    bottom_line REAL,                     -- 底部线F1值（买入信号时）
    top_line REAL,                        -- 顶部线F2值（卖出信号时）
    is_valid INTEGER NOT NULL,            -- 是否有效信号：1有效，0无效
    filter_reason TEXT,                   -- 过滤原因
    created_time TEXT NOT NULL           -- 创建时间
);
```

### 3. trade_orders - 交易指令表
```sql
CREATE TABLE trade_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_date TEXT NOT NULL,             -- 下单日期
    stock_code TEXT NOT NULL,             -- 股票代码
    order_type TEXT NOT NULL,             -- 订单类型：'BUY' 或 'SELL'
    order_reason TEXT NOT NULL,           -- 下单原因：'SIGNAL_BUY', 'SIGNAL_SELL', 'VALUE_AVERAGE'
    target_amount REAL,                   -- 目标金额
    target_shares INTEGER,                -- 目标股数
    actual_shares INTEGER,                -- 实际成交股数
    actual_price REAL,                    -- 实际成交价格
    order_status TEXT NOT NULL,           -- 订单状态：'PENDING', 'SUCCESS', 'FAILED', 'RETRY'
    retry_count INTEGER DEFAULT 0,        -- 重试次数
    error_message TEXT,                   -- 错误信息
    execution_time TEXT,                  -- 执行时间
    created_time TEXT NOT NULL           -- 创建时间
);
```

### 4. position_records - 持仓记录表
```sql
CREATE TABLE position_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    record_date TEXT NOT NULL,            -- 记录日期
    stock_code TEXT NOT NULL,             -- 股票代码
    shares INTEGER NOT NULL,              -- 持仓股数
    avg_cost REAL NOT NULL,               -- 平均成本
    market_value REAL NOT NULL,           -- 市值
    current_price REAL NOT NULL,          -- 当前价格
    period_number INTEGER,                -- 期数（仅159915有效）
    target_value REAL,                    -- 目标价值（仅159915有效）
    created_time TEXT NOT NULL           -- 创建时间
);
```

### 5. skip_periods - 跳过周期表
```sql
CREATE TABLE skip_periods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    skip_date TEXT NOT NULL,              -- 跳过日期
    period_number INTEGER NOT NULL,       -- 跳过的期数
    target_amount REAL NOT NULL,          -- 目标金额
    available_funds REAL NOT NULL,        -- 可用资金
    required_funds REAL NOT NULL,         -- 需要资金
    skip_reason TEXT NOT NULL,            -- 跳过原因
    created_time TEXT NOT NULL           -- 创建时间
);
```

### 6. trade_logs - 交易执行日志表
```sql
CREATE TABLE trade_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    log_date TEXT NOT NULL,               -- 日志日期
    log_type TEXT NOT NULL,               -- 日志类型：'INFO', 'WARNING', 'ERROR'
    operation TEXT NOT NULL,              -- 操作类型
    message TEXT NOT NULL,                -- 日志消息
    details TEXT,                         -- 详细信息（JSON格式）
    created_time TEXT NOT NULL           -- 创建时间
);
```

### 7. account_info - 账户信息表
```sql
CREATE TABLE account_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id TEXT NOT NULL,             -- 账户ID
    total_assets REAL NOT NULL,           -- 总资产
    available_cash REAL NOT NULL,         -- 可用现金
    credit_limit REAL,                    -- 融资额度
    credit_available REAL,                -- 可用融资
    update_time TEXT NOT NULL,            -- 更新时间
    created_time TEXT NOT NULL           -- 创建时间
);
```

## 索引设计

```sql
-- 信号历史表索引
CREATE INDEX idx_signal_history_date ON signal_history(signal_date);
CREATE INDEX idx_signal_history_type ON signal_history(signal_type);

-- 交易指令表索引
CREATE INDEX idx_trade_orders_date ON trade_orders(order_date);
CREATE INDEX idx_trade_orders_status ON trade_orders(order_status);
CREATE INDEX idx_trade_orders_code ON trade_orders(stock_code);

-- 持仓记录表索引
CREATE INDEX idx_position_records_date ON position_records(record_date);
CREATE INDEX idx_position_records_code ON position_records(stock_code);

-- 跳过周期表索引
CREATE INDEX idx_skip_periods_date ON skip_periods(skip_date);

-- 交易日志表索引
CREATE INDEX idx_trade_logs_date ON trade_logs(log_date);
CREATE INDEX idx_trade_logs_type ON trade_logs(log_type);
```

## 数据库初始化脚本

```python
def init_database():
    """初始化数据库表结构"""
    conn = sqlite3.connect('strategy.db')
    cursor = conn.cursor()
    
    # 执行所有CREATE TABLE语句
    # 执行所有CREATE INDEX语句
    
    # 插入初始策略状态
    cursor.execute("""
        INSERT INTO strategy_status 
        (current_phase, last_check_time, created_time, updated_time)
        VALUES ('sleeping', datetime('now'), datetime('now'), datetime('now'))
    """)
    
    conn.commit()
    conn.close()
```

## 使用说明

1. **策略状态管理**：通过`strategy_status`表维护当前策略状态
2. **信号过滤**：通过`signal_history`表实现FILTER功能
3. **交易管理**：通过`trade_orders`表管理所有交易指令和重试机制
4. **持仓跟踪**：通过`position_records`表跟踪持仓变化
5. **异常处理**：通过`skip_periods`和`trade_logs`表记录异常情况
6. **账户监控**：通过`account_info`表监控账户资金状态

所有时间字段使用ISO 8601格式存储，便于查询和排序。