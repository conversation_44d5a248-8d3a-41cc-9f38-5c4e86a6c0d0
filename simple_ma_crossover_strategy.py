#coding:gbk

"""
简单移动平均线交叉策略
策略说明：
- 5日均线上穿10日均线时买入股票（金叉信号）
- 5日均线下穿10日均线时卖出股票（死叉信号）
- 每次交易1手（100股）
- 适用于日线级别的交易

使用说明：
1. 在init函数中设置要交易的股票代码
2. 根据实际情况设置账户ID
3. 可以调整交易手数和移动平均线周期
"""

def init(ContextInfo):
    """
    策略初始化函数
    在策略开始运行时调用一次
    """
    # 设置股票池 - 可以修改为其他股票
    stock_list = ['000001.SZ']  # 平安银行，也可以设置为['600000.SH']等其他股票
    ContextInfo.set_universe(stock_list)

    # 设置账户 - 请根据实际账户ID修改
    # ContextInfo.set_account('your_account_id')  # 取消注释并填入真实账户ID

    # 初始化策略参数
    ContextInfo.MA_SHORT = 5   # 短期移动平均线周期
    ContextInfo.MA_LONG = 10   # 长期移动平均线周期
    ContextInfo.TRADE_LOTS = 1 # 每次交易手数

    # 初始化全局变量
    ContextInfo.previous_ma5 = None
    ContextInfo.previous_ma10 = None
    ContextInfo.current_position = 0  # 当前持仓状态：0-空仓，1-持仓

    print("=== 简单移动平均线交叉策略初始化完成 ===")
    print(f"股票池: {stock_list}")
    print(f"短期均线: {ContextInfo.MA_SHORT}日, 长期均线: {ContextInfo.MA_LONG}日")
    print(f"每次交易: {ContextInfo.TRADE_LOTS}手")

def handlebar(ContextInfo):
    """
    主要策略逻辑函数，每个K线结束时调用
    实现移动平均线交叉策略的核心逻辑
    """
    try:
        # 获取当前股票池
        universe = ContextInfo.get_universe()
        if not universe:
            print("股票池为空，跳过本次执行")
            return

        # 获取第一只股票（这里只处理一只股票）
        stock_code = universe[0]

        # 获取历史收盘价数据，获取足够的数据以计算长期均线
        required_days = max(ContextInfo.MA_LONG + 5, 15)  # 确保有足够的数据
        hist_data = ContextInfo.get_market_data_ex(
            fields=['close'],
            stock_code=[stock_code],
            period='1d',
            count=required_days,
            dividend_type='none'  # 不复权
        )

        if hist_data is None or len(hist_data) == 0:
            print(f"无法获取{stock_code}的历史数据")
            return

        # 调试：打印数据结构
        print(f"调试信息 - hist_data类型: {type(hist_data)}")
        print(f"调试信息 - hist_data键: {list(hist_data.keys()) if isinstance(hist_data, dict) else 'Not a dict'}")

        # 获取收盘价序列 - get_market_data_ex返回的是字典格式
        if stock_code not in hist_data:
            print(f"股票代码{stock_code}不在返回数据中")
            return

        stock_data = hist_data[stock_code]
        print(f"调试信息 - {stock_code}数据类型: {type(stock_data)}")

        if isinstance(stock_data, dict) and 'close' in stock_data:
            close_prices = stock_data['close']
        elif hasattr(stock_data, 'close'):
            close_prices = stock_data.close
        else:
            print(f"无法获取{stock_code}的收盘价数据，数据结构: {stock_data}")
            return

        print(f"调试信息 - close_prices类型: {type(close_prices)}, 长度: {len(close_prices) if hasattr(close_prices, '__len__') else 'Unknown'}")

        # 如果close_prices是列表，转换为可以计算的格式
        if isinstance(close_prices, list):
            close_prices = [price for price in close_prices if price is not None and price > 0]
        else:
            print(f"收盘价数据格式异常: {type(close_prices)}")
            return

        if len(close_prices) < ContextInfo.MA_LONG:
            print(f"历史数据不足，需要至少{ContextInfo.MA_LONG}天数据，当前只有{len(close_prices)}天")
            return

        # 计算移动平均线 - 使用列表切片和手动计算平均值
        ma_short = sum(close_prices[-ContextInfo.MA_SHORT:]) / ContextInfo.MA_SHORT  # 短期均线
        ma_long = sum(close_prices[-ContextInfo.MA_LONG:]) / ContextInfo.MA_LONG      # 长期均线

        current_price = close_prices[-1]  # 当前价格（最后一个元素）

        # 输出当前状态
        position_status = "持仓" if ContextInfo.current_position == 1 else "空仓"
        print(f"[{ContextInfo.get_bar_timetag()}] {stock_code}: 价格={current_price:.2f}, "
              f"MA{ContextInfo.MA_SHORT}={ma_short:.2f}, MA{ContextInfo.MA_LONG}={ma_long:.2f}, "
              f"状态={position_status}")

        # 判断交叉信号（需要有前一期的数据进行比较）
        if ContextInfo.previous_ma5 is not None and ContextInfo.previous_ma10 is not None:

            # 检查短期均线上穿长期均线（金叉）- 买入信号
            if (ContextInfo.previous_ma5 <= ContextInfo.previous_ma10 and
                ma_short > ma_long and
                ContextInfo.current_position == 0):

                print(f"*** 金叉信号 *** MA{ContextInfo.MA_SHORT}({ma_short:.2f}) 上穿 MA{ContextInfo.MA_LONG}({ma_long:.2f})")

                # 执行买入操作
                try:
                    trade_stocks(stock_code, ContextInfo.TRADE_LOTS, ContextInfo)
                    ContextInfo.current_position = 1
                    print(f"✓ 买入成功: {stock_code} {ContextInfo.TRADE_LOTS}手，价格: {current_price:.2f}")
                except Exception as e:
                    print(f"✗ 买入失败: {e}")

            # 检查短期均线下穿长期均线（死叉）- 卖出信号
            elif (ContextInfo.previous_ma5 >= ContextInfo.previous_ma10 and
                  ma_short < ma_long and
                  ContextInfo.current_position == 1):

                print(f"*** 死叉信号 *** MA{ContextInfo.MA_SHORT}({ma_short:.2f}) 下穿 MA{ContextInfo.MA_LONG}({ma_long:.2f})")

                # 执行卖出操作
                try:
                    trade_stocks(stock_code, -ContextInfo.TRADE_LOTS, ContextInfo)
                    ContextInfo.current_position = 0
                    print(f"✓ 卖出成功: {stock_code} {ContextInfo.TRADE_LOTS}手，价格: {current_price:.2f}")
                except Exception as e:
                    print(f"✗ 卖出失败: {e}")

        # 更新前一期的移动平均线值
        ContextInfo.previous_ma5 = ma_short
        ContextInfo.previous_ma10 = ma_long
        
    except Exception as e:
        print(f"策略执行出错: {e}")

def trade_stocks(stock_code, lots, ContextInfo, account_id=None):
    """
    股票交易函数
    stock_code: 股票代码，如 '000001.SZ'
    lots: 手数，正数买入，负数卖出
    ContextInfo: 上下文对象
    account_id: 账户ID，可选
    """
    try:
        # 计算股数（1手=100股）
        volume = abs(lots) * 100

        if account_id:
            # 如果指定了账户ID
            if lots > 0:
                # 买入 - opType=23表示股票买入
                ContextInfo.passorder(23, 1101, account_id, stock_code, 5, -1, volume, ContextInfo)
            else:
                # 卖出 - opType=24表示股票卖出
                ContextInfo.passorder(24, 1101, account_id, stock_code, 5, -1, volume, ContextInfo)
        else:
            # 使用默认账户
            if lots > 0:
                # 买入 - 使用最新价（prType=5）
                ContextInfo.passorder(23, 1101, '', stock_code, 5, -1, volume, ContextInfo)
            else:
                # 卖出 - 使用最新价（prType=5）
                ContextInfo.passorder(24, 1101, '', stock_code, 5, -1, volume, ContextInfo)

    except Exception as e:
        print(f"交易函数执行失败: {e}")
        raise e
