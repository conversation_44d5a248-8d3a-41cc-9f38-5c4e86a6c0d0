# -*- coding: utf-8 -*-
"""
价值平均+择时策略简单测试
验证核心功能是否正常工作

作者：AI Assistant
创建时间：2025-01-29
"""

import sys
import os

# 添加策略文件路径
sys.path.append('.')

# 模拟passorder函数
def passorder(*args, **kwargs):
    """模拟的passorder函数"""
    print(f"模拟交易执行: args={args}, kwargs={kwargs}")
    return True

# 将passorder添加到全局命名空间
import builtins
builtins.passorder = passorder

# 导入策略模块
try:
    from value_averaging_strategy import *
    print("✓ 策略模块导入成功")
except Exception as e:
    print(f"❌ 策略模块导入失败：{str(e)}")
    sys.exit(1)

def test_basic_functions():
    """测试基本功能"""
    print("\n开始测试基本功能...")

    try:
        # 1. 测试数据库初始化
        print("1. 测试数据库初始化...")
        init_database()
        print("✓ 数据库初始化成功")

        # 2. 测试策略状态加载
        print("2. 测试策略状态加载...")
        load_strategy_status()
        print(f"✓ 策略状态加载成功，当前阶段：{g_strategy_status['current_phase']}")

        # 3. 测试EMA计算
        print("3. 测试EMA计算...")
        test_prices = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
        ema_result = calculate_ema(test_prices, 5)
        print(f"✓ EMA计算成功，结果长度：{len(ema_result)}")

        # 4. 测试价值平均计算
        print("4. 测试价值平均计算...")
        g_strategy_status['current_phase'] = 'active'
        g_strategy_status['start_period_date'] = '2024-01-01'
        g_strategy_status['start_period_price'] = 10.0

        va_result = calculate_value_averaging(12.0)
        print(f"✓ 价值平均计算成功，期数：{va_result['current_period']}")

        # 5. 测试日志记录
        print("5. 测试日志记录...")
        log_message("INFO", "测试", "这是一条测试日志")
        print("✓ 日志记录成功")

        # 6. 测试性能摘要
        print("6. 测试性能摘要...")
        summary = get_strategy_performance_summary()
        print(f"✓ 性能摘要获取成功，总交易：{summary['total_trades']}")

        print("\n🎉 所有基本功能测试通过！")
        return True

    except Exception as e:
        print(f"❌ 基本功能测试失败：{str(e)}")
        return False
    finally:
        # 清理测试数据库
        if g_db_connection:
            g_db_connection.close()
        if os.path.exists("strategy.db"):
            os.remove("strategy.db")

def test_mock_strategy_run():
    """测试模拟策略运行"""
    print("\n开始测试模拟策略运行...")

    try:
        # 创建模拟的ContextInfo对象
        class MockContextInfo:
            def __init__(self):
                self.accid = "test_account"
                self.run_count = 0

            def is_last_bar(self):
                return True

            def get_market_data_ex(self, **kwargs):
                # 返回模拟的市场数据
                class MockData:
                    def __init__(self):
                        self.empty = False
                        self._data = [10.0] * 100

                    def __len__(self):
                        return 100

                    def __getitem__(self, key):
                        if key == 'close':
                            return self._data
                        elif key == 'high':
                            return [x * 1.1 for x in self._data]
                        elif key == 'low':
                            return [x * 0.9 for x in self._data]
                        elif key == 'open':
                            return self._data
                        return self._data

                    @property
                    def iloc(self):
                        class MockIloc:
                            def __getitem__(self, idx):
                                return 10.0
                        return MockIloc()

                return MockData()

            def get_trade_detail_data(self, *args, **kwargs):
                # 返回模拟的交易数据
                return []

        mock_context = MockContextInfo()

        # 1. 测试策略初始化
        print("1. 测试策略初始化...")
        init(mock_context)
        print("✓ 策略初始化成功")

        # 2. 测试策略运行
        print("2. 测试策略运行...")
        handlebar(mock_context)
        print("✓ 策略运行成功")

        print("\n🎉 模拟策略运行测试通过！")
        return True

    except Exception as e:
        print(f"❌ 模拟策略运行测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试数据库
        if g_db_connection:
            g_db_connection.close()
        if os.path.exists("strategy.db"):
            os.remove("strategy.db")

if __name__ == '__main__':
    print("=" * 60)
    print("价值平均+择时策略 - 简单测试")
    print("=" * 60)

    # 运行基本功能测试
    basic_test_passed = test_basic_functions()

    # 运行模拟策略测试
    strategy_test_passed = test_mock_strategy_run()

    print("\n" + "=" * 60)
    print("测试结果汇总：")
    print(f"基本功能测试：{'✓ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"策略运行测试：{'✓ 通过' if strategy_test_passed else '❌ 失败'}")

    if basic_test_passed and strategy_test_passed:
        print("\n🎉 所有测试通过！策略代码基本功能正常。")
        print("注意：实际运行时需要在iQuant平台环境中，并确保passorder等API函数可用。")
    else:
        print("\n❌ 部分测试失败，请检查代码。")

    print("=" * 60)