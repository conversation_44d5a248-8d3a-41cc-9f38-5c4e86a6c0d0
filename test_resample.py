# -*- coding: utf-8 -*-
"""
测试数据重采样功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_resample_function():
    """测试重采样函数"""
    print("=== 测试数据重采样功能 ===")
    
    # 创建模拟的日线数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    n_days = len(dates)
    
    # 模拟价格数据
    base_price = 10.0
    daily_data = pd.DataFrame({
        'open': base_price + np.random.randn(n_days) * 0.1,
        'high': base_price + np.random.randn(n_days) * 0.1 + 0.2,
        'low': base_price + np.random.randn(n_days) * 0.1 - 0.2,
        'close': base_price + np.random.randn(n_days) * 0.1,
        'volume': np.random.randint(1000, 10000, n_days),
        'amount': np.random.randint(10000, 100000, n_days)
    }, index=dates)
    
    print(f"原始日线数据: {len(daily_data)} 条记录")
    print(f"日期范围: {daily_data.index[0]} 到 {daily_data.index[-1]}")
    
    # 测试季线重采样
    try:
        quarterly_data = daily_data.resample('Q').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }).dropna()
        
        print(f"✓ 季线重采样成功: {len(quarterly_data)} 条记录")
        print(f"  季线日期范围: {quarterly_data.index[0]} 到 {quarterly_data.index[-1]}")
        
    except Exception as e:
        print(f"❌ 季线重采样失败: {str(e)}")
    
    # 测试月线重采样
    try:
        monthly_data = daily_data.resample('M').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }).dropna()
        
        print(f"✓ 月线重采样成功: {len(monthly_data)} 条记录")
        print(f"  月线日期范围: {monthly_data.index[0]} 到 {monthly_data.index[-1]}")
        
    except Exception as e:
        print(f"❌ 月线重采样失败: {str(e)}")

def test_api_change_simulation():
    """模拟API修改的效果"""
    print("\n=== 模拟API修改效果 ===")
    
    # 模拟原来的季线API调用
    print("原来的方式: 直接获取季线数据")
    print("  period='1q', count=100")
    print("  返回: 100条季线数据")
    
    # 模拟新的日线+重采样方式
    print("\n新的方式: 获取日线数据 + 重采样")
    print("  period='1d', count=6300  # 100季度 * 63交易日/季度")
    print("  然后重采样为季线")
    print("  返回: 约100条季线数据")
    
    # 计算数据量对比
    original_bars = 100  # 原来直接获取100条季线
    new_daily_bars = 100 * 63  # 新方式需要获取的日线数据
    
    print(f"\n数据量对比:")
    print(f"  原方式: {original_bars} 条季线数据")
    print(f"  新方式: {new_daily_bars} 条日线数据 -> 重采样为 ~{original_bars} 条季线数据")
    print(f"  数据量增加: {new_daily_bars / original_bars:.1f} 倍")

if __name__ == "__main__":
    test_resample_function()
    test_api_change_simulation()
    print("\n测试完成")
