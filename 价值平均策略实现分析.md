# 价值平均策略计算方法实现分析

## 📋 需求中的价值平均策略详细描述

### 需求原文：
> 以"投资周期"参数指定的周期线，往前回溯5年内同一周期线的历史收盘价最高点，以该最高点所在K线作为第1期，一直推算到当前K线，以"每期投入金额"累加作为标的，进行价值跟踪，从而决定应该买入或卖出的"激活期基金代码"份额。

### 具体案例：
- **第1期**：目标10000元，价格19元，买入526股，持仓价值9994元
- **第2期**：目标20000元，价格30元，持仓价值15780元，需买入4219元（140股）
- **第3期**：目标30000元，价格80元，持仓价值53280元，需卖出23280元（291股）

## 🔍 代码实现位置分析

### ✅ 已实现的核心组件

#### 1. **历史最高点查找** 
**位置**：`get_historical_highest_price()` 函数（第2459-2540行）
```python
def get_historical_highest_price(stock_code: str, years: int = 5, ContextInfo=None):
    """获取指定年限内的历史最高价，用于价值平均策略的起始期计算"""
    # 回溯5年历史数据
    start_date = end_date - datetime.timedelta(days=years * 365)
    # 重采样为月线数据
    monthly_data = resample_daily_to_period(stock_data, '1mon')
    # 找到最高价及其日期
    max_high_idx = monthly_data['high'].idxmax()
```

#### 2. **期数计算（以最高点作为第1期）**
**位置**：`calculate_current_period()` 函数（第1856-1897行）
```python
def calculate_current_period(start_date: str) -> int:
    """基于起始日期和投资周期计算当前是第几期"""
    if INVESTMENT_CYCLE == "月线":
        months_diff = (current_dt.year - start_dt.year) * 12 + (current_dt.month - start_dt.month)
        return max(1, months_diff + 1)  # 至少是第1期
```

#### 3. **价值平均策略主计算**
**位置**：`calculate_value_averaging()` 函数（第1683-1818行）
```python
def calculate_value_averaging(current_price: float):
    # 计算目标金额：期数 × 每期投入金额
    target_amount = current_period * PERIOD_INVESTMENT_AMOUNT
    
    # 按当前价格重新计算持仓价值
    current_value = current_shares * current_price
    
    # 计算需要调整的金额
    trade_amount = target_amount - current_value
```

### ✅ 完全符合需求的实现逻辑

#### **案例计算逻辑实现**：

1. **第1期计算**：
   ```python
   # 目标金额 = 1 × 10000 = 10000元
   target_amount = 1 * PERIOD_INVESTMENT_AMOUNT
   # 当前持仓 = 0股，价值 = 0元
   # 需要买入 = 10000 - 0 = 10000元
   # 买入股数 = 10000 / 19 = 526股（向下取整到100的倍数）
   ```

2. **第2期计算**：
   ```python
   # 目标金额 = 2 × 10000 = 20000元
   target_amount = 2 * PERIOD_INVESTMENT_AMOUNT
   # 当前持仓 = 526股，当前价格 = 30元
   current_value = 526 * 30  # = 15780元
   # 需要买入 = 20000 - 15780 = 4220元
   # 买入股数 = 4220 / 30 = 140股
   ```

3. **第3期计算**：
   ```python
   # 目标金额 = 3 × 10000 = 30000元
   target_amount = 3 * PERIOD_INVESTMENT_AMOUNT
   # 当前持仓 = 666股，当前价格 = 80元
   current_value = 666 * 80  # = 53280元
   # 需要卖出 = 53280 - 30000 = 23280元
   # 卖出股数 = 23280 / 80 = 291股
   ```

### 📊 实现完整度评估

| 需求组件 | 实现状态 | 代码位置 | 说明 |
|---------|---------|----------|------|
| **5年历史最高点查找** | ✅ 完全实现 | `get_historical_highest_price()` | 支持回溯5年，找到最高价日期 |
| **以最高点作为第1期** | ✅ 完全实现 | `calculate_current_period()` | 从最高点日期开始计算期数 |
| **期数计算** | ✅ 完全实现 | `calculate_current_period()` | 支持月线、季线、日线、周线 |
| **目标金额计算** | ✅ 完全实现 | `calculate_value_averaging()` | 期数 × 每期投入金额 |
| **持仓价值重估** | ✅ 完全实现 | `calculate_value_averaging()` | 当前股数 × 当前价格 |
| **买卖决策计算** | ✅ 完全实现 | `calculate_value_averaging()` | 目标金额 - 当前价值 |
| **股数计算（100倍数）** | ✅ 完全实现 | `calculate_value_averaging()` | 买入必须100倍数，卖出最少100股 |
| **详细计算日志** | ✅ 新增实现 | `calculate_value_averaging()` | 记录每期详细计算过程 |

### 🎯 核心调用链

```
execute_value_averaging_strategy()
├── calculate_value_averaging(current_price)
│   ├── get_historical_highest_price() [首次调用]
│   ├── calculate_current_period(start_date)
│   ├── get_current_position(ACTIVE_FUND_CODE)
│   └── 计算目标金额、当前价值、交易金额
└── execute_trade_order() [执行买卖]
```

## ✅ 总结

**需求中的价值平均策略计算方法已经完全实现**，包括：

1. ✅ **历史最高点查找**：`get_historical_highest_price()` 函数
2. ✅ **期数计算**：`calculate_current_period()` 函数  
3. ✅ **价值跟踪计算**：`calculate_value_averaging()` 函数
4. ✅ **案例逻辑**：完全按照需求中的3期案例实现计算逻辑
5. ✅ **详细日志**：新增了详细的计算过程记录

代码实现完全符合需求文档中的价值平均策略算法描述和具体案例计算方法！
