# 简单移动平均线交叉策略使用说明

## 策略概述

这是一个基于iQuant平台的简单移动平均线交叉策略，实现了经典的双均线交易系统：

- **买入信号（金叉）**：当5日移动平均线从下方穿越10日移动平均线时买入
- **卖出信号（死叉）**：当5日移动平均线从上方穿越10日移动平均线时卖出

## 策略特点

1. **简单易懂**：基于经典的技术分析指标
2. **参数可调**：可以轻松修改均线周期和交易手数
3. **风险控制**：每次只交易固定手数，避免过度交易
4. **日志详细**：提供详细的交易信号和执行日志

## 文件说明

- `simple_ma_crossover_strategy.py` - 主策略文件
- `简单移动平均线策略使用说明.md` - 本说明文档

## 使用步骤

### 1. 配置策略参数

在 `init()` 函数中修改以下参数：

```python
# 设置股票池
stock_list = ['000001.SZ']  # 可以修改为其他股票代码

# 设置账户ID（重要！）
ContextInfo.set_account('your_account_id')  # 取消注释并填入真实账户ID

# 策略参数
ContextInfo.MA_SHORT = 5   # 短期均线周期，默认5日
ContextInfo.MA_LONG = 10   # 长期均线周期，默认10日
ContextInfo.TRADE_LOTS = 1 # 每次交易手数，默认1手
```

### 2. 股票代码格式

确保使用正确的股票代码格式：
- 深圳股票：`'000001.SZ'`（平安银行）
- 上海股票：`'600000.SH'`（浦发银行）

### 3. 账户设置

**重要**：必须设置正确的账户ID才能进行实际交易：

```python
# 在init函数中取消注释并填入真实账户ID
ContextInfo.set_account('your_real_account_id')
```

### 4. 运行策略

1. 将策略文件导入iQuant平台
2. 设置回测或实盘交易参数
3. 点击运行

## 策略逻辑详解

### 初始化阶段（init函数）

1. 设置股票池和基准
2. 配置策略参数
3. 初始化全局变量

### 交易执行阶段（handlebar函数）

1. **数据获取**：获取足够的历史收盘价数据
2. **指标计算**：计算短期和长期移动平均线
3. **信号判断**：检测均线交叉信号
4. **交易执行**：根据信号执行买入或卖出操作
5. **状态更新**：更新持仓状态和历史数据

### 交易信号

#### 买入信号（金叉）
- 前一期：短期均线 ≤ 长期均线
- 当前期：短期均线 > 长期均线
- 当前状态：空仓

#### 卖出信号（死叉）
- 前一期：短期均线 ≥ 长期均线
- 当前期：短期均线 < 长期均线
- 当前状态：持仓

## 参数调整建议

### 均线周期调整

```python
# 更保守的设置（减少交易频率）
ContextInfo.MA_SHORT = 10  # 10日均线
ContextInfo.MA_LONG = 20   # 20日均线

# 更激进的设置（增加交易频率）
ContextInfo.MA_SHORT = 3   # 3日均线
ContextInfo.MA_LONG = 7    # 7日均线
```

### 交易手数调整

```python
# 小资金账户
ContextInfo.TRADE_LOTS = 1  # 每次1手

# 大资金账户
ContextInfo.TRADE_LOTS = 5  # 每次5手
```

## 风险提示

1. **历史表现不代表未来收益**：移动平均线策略在震荡市场中可能产生频繁的假信号
2. **交易成本**：频繁交易会产生手续费，需要考虑成本因素
3. **滑点风险**：实际成交价格可能与预期价格存在差异
4. **账户安全**：确保账户ID和密码安全，避免泄露

## 常见问题

### Q1: 策略不执行交易怎么办？
A1: 检查以下几点：
- 账户ID是否正确设置
- 股票代码格式是否正确
- 是否有足够的历史数据
- 账户是否有足够资金

### Q2: 如何查看交易日志？
A2: 策略会在控制台输出详细日志，包括：
- 当前价格和均线值
- 交叉信号检测
- 交易执行结果

### Q3: 可以同时交易多只股票吗？
A3: 当前版本只支持单只股票交易。如需多股票交易，需要修改代码逻辑。

### Q4: 如何优化策略表现？
A4: 可以考虑：
- 调整均线周期参数
- 添加其他技术指标过滤
- 设置止损止盈条件
- 优化资金管理

## 技术支持

如有问题，请参考：
1. iQuant官方API文档
2. 策略代码中的注释说明
3. 控制台输出的错误信息

## 免责声明

本策略仅供学习和研究使用，不构成投资建议。使用者应当充分了解股票投资风险，并根据自身情况谨慎决策。策略开发者不对使用本策略产生的任何损失承担责任。
